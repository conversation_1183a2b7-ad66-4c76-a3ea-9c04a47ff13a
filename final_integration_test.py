#!/usr/bin/env python3
"""
Final integration test for the complete fraud detection platform
"""
import requests
import json
import time
import psycopg2
from datetime import datetime

def test_postgresql_connection():
    """Test PostgreSQL database connection"""
    print("🗄️ Testing PostgreSQL Connection...")
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="fraud_detection",
            user="postgres",
            password="postgres",
            port="5432"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ PostgreSQL connected successfully")
        print(f"   Version: {version[0][:50]}...")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_model_service():
    """Test Model Service with PostgreSQL"""
    print("\n🤖 Testing Model Service...")
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Model Service health: {health_data['status']}")
        else:
            print(f"❌ Model Service health failed: HTTP {response.status_code}")
            return False
        
        # Test fraud scoring
        test_transaction = {
            "transactions": [{
                "transaction_id": "integration_test_001",
                "step": 1,
                "type": "CASH_OUT",
                "amount": 150000.0,
                "nameOrig": "C123456789",
                "oldbalanceOrg": 200000.0,
                "newbalanceOrig": 0.0,
                "nameDest": "M987654321",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 150000.0
            }]
        }
        
        response = requests.post("http://localhost:8001/score", json=test_transaction, timeout=10)
        if response.status_code == 200:
            result = response.json()
            risk_score = result["results"][0]["risk"]
            print(f"✅ Fraud scoring working: Risk score {risk_score:.3f}")
            return True
        else:
            print(f"❌ Fraud scoring failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Model Service test failed: {e}")
        return False

def test_react_dashboard():
    """Test React Dashboard"""
    print("\n🌐 Testing React Dashboard...")
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200 and "Fraud Detection Platform" in response.text:
            print("✅ React Dashboard accessible and loading correctly")
            return True
        else:
            print(f"❌ React Dashboard failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ React Dashboard test failed: {e}")
        return False

def test_api_documentation():
    """Test API documentation accessibility"""
    print("\n📚 Testing API Documentation...")
    try:
        response = requests.get("http://localhost:8001/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API documentation accessible")
            return True
        else:
            print(f"❌ API documentation failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API documentation test failed: {e}")
        return False

def test_end_to_end_workflow():
    """Test complete end-to-end workflow"""
    print("\n🔄 Testing End-to-End Workflow...")
    
    # Test multiple transaction scenarios
    test_scenarios = [
        {
            "name": "Low Risk Payment",
            "transaction": {
                "transaction_id": "e2e_low_risk",
                "step": 1,
                "type": "PAYMENT",
                "amount": 25.0,
                "nameOrig": "C111111111",
                "oldbalanceOrg": 1000.0,
                "newbalanceOrig": 975.0,
                "nameDest": "M222222222",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 25.0
            },
            "expected_risk": "low"
        },
        {
            "name": "High Risk Cash Out",
            "transaction": {
                "transaction_id": "e2e_high_risk",
                "step": 2,
                "type": "CASH_OUT",
                "amount": 500000.0,
                "nameOrig": "C333333333",
                "oldbalanceOrg": 500000.0,
                "newbalanceOrig": 0.0,
                "nameDest": "M444444444",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 500000.0
            },
            "expected_risk": "high"
        }
    ]
    
    successful_tests = 0
    
    for scenario in test_scenarios:
        try:
            response = requests.post(
                "http://localhost:8001/score",
                json={"transactions": [scenario["transaction"]]},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                risk_score = result["results"][0]["risk"]
                
                # Determine risk level
                if risk_score >= 0.8:
                    risk_level = "high"
                elif risk_score >= 0.5:
                    risk_level = "medium"
                else:
                    risk_level = "low"
                
                print(f"✅ {scenario['name']}: Risk {risk_score:.3f} ({risk_level})")
                
                if risk_level == scenario["expected_risk"] or (scenario["expected_risk"] == "high" and risk_level in ["high", "medium"]):
                    successful_tests += 1
                else:
                    print(f"   ⚠️  Expected {scenario['expected_risk']} risk, got {risk_level}")
            else:
                print(f"❌ {scenario['name']}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {scenario['name']}: {e}")
    
    return successful_tests == len(test_scenarios)

def main():
    """Run complete integration test"""
    print("🚀 FRAUD DETECTION PLATFORM - FINAL INTEGRATION TEST")
    print("=" * 70)
    
    tests = [
        ("PostgreSQL Database", test_postgresql_connection),
        ("Model Service", test_model_service),
        ("React Dashboard", test_react_dashboard),
        ("API Documentation", test_api_documentation),
        ("End-to-End Workflow", test_end_to_end_workflow)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed_tests += 1
    
    # Final Summary
    print("\n" + "=" * 70)
    print("🏆 FINAL INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("The fraud detection platform is fully functional with:")
        print("✅ PostgreSQL Database Integration")
        print("✅ Model Service with Fraud Scoring")
        print("✅ React Dashboard User Interface")
        print("✅ Complete End-to-End Workflow")
    elif passed_tests >= 4:
        print("\n✅ PLATFORM MOSTLY FUNCTIONAL!")
        print("Core components are working with minor issues.")
    else:
        print("\n⚠️ PLATFORM NEEDS ATTENTION!")
        print("Multiple components require fixes.")
    
    print("\n🔗 ACCESS POINTS:")
    print("• React Dashboard: http://localhost:3000")
    print("• Model Service API: http://localhost:8001")
    print("• API Documentation: http://localhost:8001/docs")
    print("• PostgreSQL Database: localhost:5432/fraud_detection")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    main()
