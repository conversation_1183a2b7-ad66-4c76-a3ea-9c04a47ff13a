#!/usr/bin/env python3
"""
Simple startup script for the model service
"""
import sys
import os

# Add the model-service/src directory to Python path
model_service_path = os.path.join(os.path.dirname(__file__), 'model-service', 'src')
sys.path.insert(0, model_service_path)

# Set environment variables for local development
os.environ['MODEL_PATH'] = os.path.join(os.path.dirname(__file__), 'model-service', 'models', 'fraud_detection_model.pkl')
os.environ['DATABASE_URL'] = 'postgresql://postgres:postgres@localhost:5432/fraud_detection'

try:
    from app.main import app
    import uvicorn

    print("Starting Model Service on http://localhost:8001")
    print("Model path:", os.environ.get('MODEL_PATH'))
    print("Database URL:", os.environ.get('DATABASE_URL'))

    uvicorn.run(app, host="0.0.0.0", port=8001)

except Exception as e:
    print(f"Error starting model service: {e}")
    import traceback
    traceback.print_exc()
