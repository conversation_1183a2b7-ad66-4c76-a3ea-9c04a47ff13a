# Fraud Detection Platform - Local Development Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/fraud_detection
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=fraud_detection

# Model Service Configuration
MODEL_SERVICE_PORT=8000
MODEL_PATH=./model-service/models/fraud_detection_model.pkl
USE_ENHANCED_MODEL=true
LOG_LEVEL=INFO

# Ingest Service Configuration
INGEST_SERVICE_PORT=9000
KAFKA_BOOTSTRAP_SERVERS=localhost:29092
KAFKA_TOPIC_TRANSACTIONS=transactions
KAFKA_TOPIC_INVALID=transactions.invalid
MODEL_SERVICE_URL=http://localhost:8000/score
RISK_THRESHOLD=0.8

# Dashboard Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:9000/ws/txns
REACT_APP_MODEL_SERVICE_URL=http://localhost:8000
REACT_APP_INGEST_SERVICE_URL=http://localhost:9000
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true

# Kafka Configuration
KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=localhost:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092,PLAINTEXT_HOST://localhost:29092
KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
KAFKA_AUTO_CREATE_TOPICS_ENABLE=true

# Zookeeper Configuration
ZOOKEEPER_CLIENT_PORT=2181
ZOOKEEPER_TICK_TIME=2000

# Security Configuration
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Slack Integration (Optional)
SLACK_WEBHOOK_URL=

# Email Configuration (Optional)
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=<EMAIL>

# Development Settings
DEBUG=true
DEVELOPMENT_MODE=true
HOT_RELOAD=true
